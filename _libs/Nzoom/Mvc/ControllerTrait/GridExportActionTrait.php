<?php

namespace Nzoom\Mvc\ControllerTrait;

use Nzoom\Export\ExportService;

/**
 * Trait for handling grid export actions
 *
 * @method getViewer(): Viewer
 * @property \Registry $registry
 * @property string $module
 * @property string $controller
 * @property string $modelName
 * @property string $modelFactoryName
 */
trait GridExportActionTrait
{
    public function createExportAction(string $module_check, array $types, array $typeSections): ?array
    {
        if (
            ! $this->checkActionPermissions($module_check, 'export')
            || in_array($module_check, $this->systemExportModules)
        ) {
            return null;
        }

        return [
            'options' => $this->prepareExportOptions2($types, $typeSections),
            'ajax_no' => 1,
            'disable_items_before_execute' => 'diselectItemsBeforeMultiAction(this);',
        ];
    }
    /**
     * Prepare export options with plugins where needed
     *
     * This version doesn't use saveSearchParams, instead it takes all necessary parameters as arguments
     *
     * @param array $types Array of available types
     * @param array $sections Array of available sections
     * @param array $filtersHide Filters to hide
     * @return array The prepared array with options for export
     */
    public function prepareExportOptions2(
        array $types = [],
        array $sections = [],
        array $filtersHide = ['group_tables'=>false, 'separator'=>true]
    ): array {
        // Initialize filter visibility settings
        $filtersHide = $this->initializeFilterVisibility($filtersHide);

        $selectedType = $this->firstOrZero($types);
        $selectedSection = $this->firstOrZero($sections);
        // Build the export options array
        $exportOptions = $this->buildBaseExportOptions($filtersHide, $types, $selectedSection);

        $exportOptions = array_merge(
            $exportOptions,
            $this->getPluginOptions($selectedType, $selectedSection));

        // Add format and delimiter options
        $exportOptions = array_merge(
            $exportOptions,
            $this->getFormatOptions($filtersHide),
            $this->getGroupTablesOptions($filtersHide),
            $this->getDelimiterOptions($filtersHide)
        );

        return $exportOptions;
    }

    /**
     * Initialize filter visibility settings
     *
     * @param array $filtersHide Initial filter visibility settings
     * @return array Updated filter visibility settings
     */
    private function initializeFilterVisibility(array $filtersHide): array
    {
        foreach (\Exports::$filtersToStore as $param) {
            if (!isset($filtersHide[$param])) {
                $filtersHide[$param] = false;
            }
        }

        return $filtersHide;
    }

    private function firstOrZero(array $subject): int
    {
        if (count($subject) === 1) {
            return (int) reset($subject);
        }
        return 0;
    }

    /**
     * Get plugin options for export
     *
     * @param int $selectedType Selected type ID
     * @param int $selectedSection Selected section ID
     * @return array Plugin data including options and settings
     */
    private function getPluginOptions(int $selectedType, int $selectedSection): array
    {
        // Try to get plugins for the listed model and model_type
        $filters = [
            'model' => $this->modelName,
            'model_type' => $selectedType ?: $selectedSection,
            'sanitize' => true
        ];

        // Get all plugins for this model
        $plugins = \Exports::search($this->registry, $filters);

        if (empty($plugins)) {
            return [];
        }
        // Get last used plugin from session
        $lastExportPlugins = $this->registry['session']->get('last_export_plugins');
        $lastExportPluginSessionParam = sprintf(
            '%s_%s_type_%s',
            $this->module,
            $this->controller,
            $selectedType
        );

        $lastExportPlugin = $lastExportPlugins[$lastExportPluginSessionParam] ?? 'standard';

        // Add standard plugin option
        $pluginOptions = [
            'label' => $this->i18n('standard_export'),
            'option_value' => self::EXPORT_PLUGIN_STANDARD
        ];

        // Add all plugins to the options
        foreach ($plugins as $plugin) {
            $pluginOptions[] = [
                'label' => $plugin->get('name'),
                'option_value' => $plugin->get('id')
            ];
        }

        // Prepare export options for plugins
        return [
            [
                'custom_id' => 'plugin',
                'name' => 'plugin',
                'type' => 'dropdown',
                'required' => 1,
                'onchange' => 'selectExport(this, \'' . implode(',', \Exports::$filtersToStore) . '\')',
                'label' => $this->i18n('export_plugin'),
                'help' => $this->i18n('help_export_plugin'),
                'options' => $pluginOptions,
                'value' => $lastExportPlugin
            ],
            [
                'custom_id' => 'previous_plugin',
                'name' => 'previous_plugin',
                'type' => 'hidden',
                'hidden' => 1,
                'value' => $lastExportPlugin
            ],
        ];
    }

    /**
     * Build the base export options array
     *
     * @param array $filtersHide Filters to hide
     * @param array $types Available types
     * @param int $selectedSection Selected section ID
     * @return array Base export options
     */
    private function buildBaseExportOptions(array $filtersHide, array $types, int $selectedSection): array
    {
        // Prepare export options
        $exportOptions = [
            [
                'custom_id' => 'export_previous_action',
                'name'      => 'export_previous_action',
                'type'      => 'hidden',
                'value'     => 'list',
                'hidden'    => 1,
            ],
            [
                'custom_id' => 'file_name',
                'name' => 'file_name',
                'type' => 'text',
                'required' => 1,
                'label' => $this->i18n('file_name'),
                'help' => $this->i18n('file_name'),
                'value' => $this->modelFactoryName,
                'hidden' => (isset($filtersHide['file_name'])) ? $filtersHide['file_name'] : 0,
            ],
        ];

        // Add type or section ID if needed
        if (count($types) === 1) {
            $exportOptions[] = [
                'name' => 'type_id',
                'type' => 'hidden',
                'value' => reset($types),
                'hidden' => 1,
            ];
        } elseif ($selectedSection) {
            $exportOptions[] = [
                'name' => 'type_section_id',
                'type' => 'hidden',
                'value' => $selectedSection,
                'hidden' => 1,
            ];
        }

        return $exportOptions;
    }

    /**
     * Get format options for export
     *
     * @param array $filtersHide Filters to hide
     * @return array Format options
     */
    private function getFormatOptions(array $filtersHide): array
    {
        return [
            [
                'custom_id' => 'format',
                'name' => 'format',
                'type' => 'dropdown',
                'required' => 1,
                'label' => $this->i18n('file_format'),
                'help' => $this->i18n('file_format'),
                'options' => [
                    [
                        'label' => 'Excel (XLSX)',
                        'option_value' => 'xlsx'
                    ],
                    [
                        'label' => 'Excel (XLS)',
                        'option_value' => 'xls'
                    ],
                    [
                        'label' => 'CSV',
                        'option_value' => 'csv'
                    ],
                ],
                'onchange' => 'toggleExportFormat(this)',
                'value' => 'xlsx',
                'hidden' => (isset($filtersHide['format'])) ? $filtersHide['format'] : 0,
            ],
        ];
    }

    /**
     * Get group tables options for export
     *
     * @return array Group tables options
     */
    private function getGroupTablesOptions(array $filtersHide): array
    {
        return [
            [
                'custom_id' => 'group_tables',
                'name' => 'group_tables',
                'type' => 'radio',
                'required' => 1,
                'options_align' => 'horizontal',
                'label' => $this->i18n('include_group_tables'),
                'help' => $this->i18n('include_group_tables'),
                'options' => [
                    [
                        'label' => $this->i18n('yes'),
                        'option_value' => 1
                    ],
                    [
                        'label' => $this->i18n('no'),
                        'option_value' => 0
                    ],
                ],
                'value' => 0,
                'hidden' => (isset($filtersHide['group_tables'])) ? $filtersHide['group_tables'] : 0,
            ]
        ];
    }

    /**
     * Get delimiter options for CSV export
     *
     * @param array $filtersHide Filters to hide
     * @return array Delimiter options
     */
    private function getDelimiterOptions(array $filtersHide): array
    {
        return [
            [
                'custom_id' => 'separator',
                'name' => 'separator',
                'type' => 'dropdown',
                'required' => 1,
                'label' => $this->i18n('separator'),
                'help' => $this->i18n('separator'),
                'options' => [
                    [
                        'label' => $this->i18n('delimiter_comma'),
                        'option_value' => 'comma'
                    ],
                    [
                        'label' => $this->i18n('delimiter_semicolon'),
                        'option_value' => 'semicolon'
                    ],
                    [
                        'label' => $this->i18n('delimiter_tab'),
                        'option_value' => 'tab'
                    ],
                ],
                'value' => 'comma',
                'hidden' => (isset($filtersHide['separator'])) ? $filtersHide['separator'] : 0,
            ]
        ];
    }

    public function _export2()
    {
        /** @var \Registry $registry */
        $registry = $this->registry;
        /** @var \Request $request */
        $request = $registry['request'];

        // Get export format and items from request
        $format = $request->getPost('format', 'csv');
        $items = $request->getPost('items', []);
        $filename = $request->getPost('file_name', '');
        $type_id = $request->getPost('type_id', null);
        $type_section_id = $request->getPost('type_section_id', null);

        // Validate items
        if (empty($items) || !is_array($items)) {
            $this->_handleExportError('No items selected for export');
            return;
        }

        // Convert items to integers for safety
        $items = array_map('intval', $items);

        $user_id = (int)$registry['currentUser']->get('id');
        $role_id = (int)$registry['currentUser']->get('role');

        $outlook = \Outlooks::getOutlook(
            $registry,
            !empty($type_section_id),
            (int) ($type_id ?? $type_section_id ?? 0),
            $user_id,
            $role_id,
            $this->module,
            $this->controller
        );

        // Check if we need to get tags
        $modelFields = [];
        foreach ($outlook->get('current_custom_fields') as $v) {
            if (!$v['position']) {
                // no position means not visible
                continue;
            }
            $modelFields[$v['name']] = $v;
        }

        $modelFieldsNames = array_column($modelFields, 'name');
        if (in_array('tags', $modelFieldsNames)) {
            $this->registry->set('getTags', true, true);
        }

        $modelFactoryName = $this->modelFactoryName;
        $alias = $modelFactoryName::getAlias($registry['module'], $registry['controller'], $registry['action']);

        $idsList = implode(',', $items);
        $where = [
            "{$alias}.id IN ($idsList)"
        ];


        $filters = [
            'where' => $where,
            'get_fields' => $outlook->get('current_custom_fields'),
        ];

        // Get factory class name with namespace if needed
        $factoryClass = $this->modelFactoryName;
        if (strpos($factoryClass, '\\') === false && class_exists("\\" . $factoryClass)) {
            $factoryClass = "\\". $factoryClass;
        }

        // Validate factory class has search method
        if (!method_exists($factoryClass, 'search')) {
            throw new \Exception("Factory class $factoryClass does not have a search method");
        }

        // Generate filename if empty
        if (empty($filename)) {
            $filename = strtolower($this->module . '_' . $this->controller . '_export_' . date('Y-m-d_H-i-s'));
        }

        // Create export service and perform export
        try {
            $exportService = new ExportService($this->registry, $this->module, $this->controller, $format);

            $groupTables = $request->getPost('group_tables', false);
            if (in_array(strtolower($format), ['xls', 'xlsx']) && $groupTables) {
                $data = $exportService->createExportDataWithTables($outlook, $filters, $factoryClass);
            } else {
                $data = $exportService->createExportData($outlook, $filters, $factoryClass);
            }

            // Get export options for constrained sizing (Excel only)
            $exportOptions = $this->getExportOptions($request, $format);

            $exportService->export($filename, $data, $exportOptions);
        } catch (\Exception $e) {
            $this->_handleExportError('Export failed: ' . $e->getMessage());
        }
        exit;
    }

    /**
     * Get export options from request for constrained sizing
     *
     * @param \Request $request
     * @param string $format
     * @return array
     */
    protected function getExportOptions(\Request $request, string $format): array
    {
        $options = [];

        // Only apply sizing constraints for Excel formats
        if (in_array(strtolower($format), ['xls', 'xlsx'])) {
            // Get sizing options from request (with sensible defaults)
            $maxColumnWidth = $request->getPost('max_column_width', 50.0);
            $maxRowHeight = $request->getPost('max_row_height', 100.0);
            $chunkSize = $request->getPost('chunk_size', 1000);
            $groupTables = $request->getPost('group_tables', false);
            $includeEnumeration = $request->getPost('include_enumeration', false);


            // Validate and sanitize the values
            $options['max_column_width'] = max(10.0, min(255.0, (float) $maxColumnWidth));
            $options['max_row_height'] = max(15.0, min(500.0, (float) $maxRowHeight));
            $options['chunk_size'] = max(100, min(5000, (int) $chunkSize));
            $options['group_tables'] =  $groupTables;
            $options['include_enumeration'] = (bool) $includeEnumeration;

        }

        if (in_array(strtolower($format), ['csv'])) {
            // Adapter default is ISO Y-m-d however, legacy behaviour is d.m.Y,
            // so we add support for options that can control it but assume legacy as default.
            $dateFormat = $request->getPost('date_format', 'd.m.Y');
            $dateTimeFormat = $request->getPost('datetime_format', 'd.m.Y H:i:s');
            $options['date_format'] = $dateFormat;
            $options['datetime_format'] = $dateTimeFormat;
        }

        return $options;
    }

    /**
     * Handle export error
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code to use (default: 400)
     * @return void
     */
    protected function _handleExportError($message, $statusCode = 400)
    {
        // Log the error if logger is available
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->error('Export error: ' . $message);
        }

        // Set error message in registry for AJAX response
        $this->registry->set('ajax_result', json_encode([
            'error' => $message,
            'status' => 'error',
            'timestamp' => date('Y-m-d H:i:s')
        ]), true);

        // If this is an AJAX request, send appropriate headers
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {

            http_response_code($statusCode);
            header('Content-Type: application/json');
            echo $this->registry->get('ajax_result');
            exit;
        }

        // For non-AJAX requests, we'll let the controller handle the response
    }
}
